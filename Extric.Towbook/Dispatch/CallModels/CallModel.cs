using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using A = Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using Extric.Towbook.Auctions;
using Extric.Towbook.Accounts;
using Extric.Towbook.Company;
using Extric.Towbook.Generated;
using Extric.Towbook.Vehicle;
using Extric.Towbook.Company.Accounting;
using Newtonsoft.Json;
using Extric.Towbook.Tasks;
using System.Threading.Tasks;
using System.Runtime.CompilerServices;


namespace Extric.Towbook.Dispatch.CallModels
{
    public enum CallPriority
    {
        Normal = 0,
        High = 1,
        Low = 2
    }

    public class CosmosCallModel : CallModel
    {
        public long? _ts { get; set; }
    }

    public sealed class DeletionModel
    {
        public int UserId { get; set; }
        public string IpAddress { get; set; }
        public DateTime Date { get; set; }
    }

    public partial class CallModel
    {
        public int Id { get; set; }
        public int CallNumber { get; set; }

        public Entry.EntryType? Type { get; set; }

        public int[] Groups { get; set; }

        public int CompanyId { get; set; }
        public long Version { get; set; }
        public DateTime? LastModifiedTimestamp { get; set; }
        public DeletionModel DeletionDetails { get; set; }

        // flat notes. also support a list of notes
        public string Notes { get; set; }

        public PartialAccountModel Account { get; set; }
        public SubcontractorModel Subcontractor { get; set; }

        public PartialUserModel Owner { get; set; }

        public IEnumerable<CallContactModel> Contacts { get; set; }

        /// <summary>
        /// Represents the locations for the call (Source, Destination, etc)
        /// </summary>
        /// <remarks>
        /// This will replace the use of TowDestination/TowSource
        /// </remarks>
        public CallWaypointModel[] Waypoints { get; set; }

        /// <summary>
        /// Tags to represent the call (Paid, Unpaid, Customer Dispute, No Commission, No Charge)
        /// </summary>
        public int[] Tags { get; set; }

        /// <summary>
        /// A list of statements that this call has been placed on.
        /// </summary>
        public int[] Statements { get; set; }

        public string TowSource { get; set; }
        public string TowDestination { get; set; }

        public int CallType
        {
            get
            {
                if (TowDestination != null && TowDestination.Trim() != "")
                    return 1;
                else
                    return 2;
            }
        }

        public long? ChatId { get; set; }

        public string ChatChannelSid { get; set; }

        [EnumDataType(typeof(CallPriority))]
        public CallPriority? Priority { get; set; }
        public PartialReasonModel Reason { get; set; }
        [Obsolete]
        public PartialTruckModel Truck { get; set; }
        [Obsolete]
        public PartialDriverModel Driver { get; set; }

        public CallInvoiceItemModel[] InvoiceItems { get; set; }

        public string InvoiceNumber { get; set; }

        public decimal InvoiceSubtotal { get; set; }
        public decimal InvoiceTax { get; set; }
        public decimal InvoiceTotal { get; set; }
        public bool? InvoiceTaxExempt { get; set; }

        public int? BillToAccountId { get; set; }
        public string BillToAccountName { get; set; }

        public string PurchaseOrderNumber { get; set; }

        /// <summary>
        /// Exposes the balances grouped by Class.
        /// </summary>
        public ClassBalanceModel[] BalanceByClass { get; set; }

        // use this instead of hard-coded statuses. allows for completely dynamic statuses. 

        public CallStatusUpdateModel Status { get; set; }
        public int[] Statuses { get; set; }
        public CallStatusUpdateModel[] StatusUpdateHistory { get; set; }
        public CallAttributeValueModel[] Attributes { get; set; }

        public int[] ActiveUsers { get; set; }

        /// <summary>
        /// Represents the assets (vehicles, trailers, etc) associated with the call
        /// </summary>
        public CallAssetModel[] Assets { get; set; }

        public DateTime? CreateDate { get; set; }
        public string CancellationReason { get; set; }

        #region Status Times... consider changing this to work differently to allow custom statuses in the future

        public DateTime? DispatchTime { get; set; }
        public DateTime? EnrouteTime { get; set; }
        public DateTime? ArrivalETA { get; set; }
        public DateTime? ArrivalTime { get; set; }
        public DateTime? TowTime { get; set; }
        public DateTime? DestinationArrivalTime { get; set; }
        public DateTime? CompletionTime { get; set; }

        /// <summary>
        /// Determines when we calculate the driver will arrive. Contains both estimated time of arrival, 
        /// and distance remaining from last calculation.
        /// </summary>
        public CalculatedEtaModel CalculatedEta { get; set; }

        public class CalculatedEtaModel
        {
            public DateTime LastUpdated { get; set; }
            public DateTime Eta { get; set; }
            public decimal DistanceRemaining { get; set; }
        }

        #endregion

        public decimal BalanceDue { get; set; }
        public decimal PaymentsApplied { get; set; }

        public IEnumerable<PaymentModel> Payments { get; set; }

        public int? InvoiceStatusId { get; set; }

        /// <summary>
        /// Whether destination is Impound or not.
        /// </summary>
        public bool? Impound { get; set; }
        public int? ImpoundLotId { get; set; }
        public ImpoundDetailsModel ImpoundDetails { get; set; }
        public AuctionDetailsModel AuctionDetails { get; set; }

        /// <summary>
        /// Represents Commissions for the call.
        /// Should only be returned if the user is a manger, the user has 
        /// permission to view commissions, or the call is assigned to the
        /// driver of the user viewing the call.
        /// </summary>
        public CallCommissionModel Commissions { get; set; }

        public CallTipModel Tips { get; set; }

        /// <summary>
        /// Read-only URL that the user can visit with tips for towing the vehicle.
        /// </summary>
        public string ReferenceUrl { get; set; }
        public string ReferenceUrlName { get; set; }

        /// <summary>
        /// Insights that return how many photos, pushed to quickbooks, etc. Not available to api calls from android or ios app.
        /// </summary>
        public IDictionary<string, object> Insights { get; set; }

        /// <summary>
        /// Represents available actions that can be performed on the call. 
        /// </summary>
        public string[] AvailableActions { get; set; } = Array.Empty<string>();


        /// <summary>
        /// Represents available pusher channels that can be used for this call.
        /// </summary>
        public IEnumerable<CallChannelModel> Channels { get; set; }

        /// <summary>
        /// Represents whether an attribute for billing notes exists on the call.
        /// </summary>
        public bool HasBillingNotes() => 
            Attributes != null && Attributes.Any(a => a.AttributeId == Dispatch.AttributeValue.BUILTIN_BILLING_NOTES);

        public static CallModel Map(Entry x)
        {
            return Map(x, null, null);
        }

        public static async Task<CallModel> MapAsync(Entry x)
        {
            return await MapAsync(x, null, null);
        }


        // TODO: move this to extric.towbook
        public class RealTimeRecord
        {
            public int CallId { get; set; }
            public int CallStatus { get; set; }

            public int? DriverId { get; set; }
            public int? UserId { get; set; }

            public decimal? CallLatitude { get; set; }
            public decimal? CallLongitude { get; set; }

            public decimal? UserLatitude { get; set; }
            public decimal? UserLongitude { get; set; }

            public DateTime? UserTimestamp { get; set; }

            public DateTime? CallEta { get; set; }
            public DateTime? DriverEta { get; set; }
            public DateTime LastUpdated { get; set; }

            public decimal? DriverDistanceRemaining { get; set; }
        }

        public static CallModel Map(Entry x, 
            IEnumerable<CallInsightHelper.CallInsightModel> insights, 
            IEnumerable<InvoicePayment> payments, 
            IDictionary<string,object> prefilledInsights = null,
            Impounds.Impound impound = null,
            IEnumerable<VehicleTitle> vehicleTitles = null,
            IEnumerable<EntryAuctionDetail> auctionDetails = null)
        {
            if (x == null)
                return null;

            var r = new CallModel()
            {
                Id = x.Id,
                CallNumber = x.CallNumber,
                CompanyId = x.CompanyId,
                Type = x.Type,
                Reason = PartialReasonModel.Map(x.Reason),
                TowSource = x.TowSource,
                TowDestination = x.TowDestination,

                Statements = x.Statements ?? Array.Empty<int>(),
                Version = x.Version,
                LastModifiedTimestamp = x.LastModifiedTimestamp,

                Contacts = CallContactModel.MapDomainObjectListToModel(x.Contacts),
                Account = PartialAccountModel.MapDomainToModel(x.Account),
                Subcontractor = SubcontractorModel.Map(x),
                InvoiceItems = CallInvoiceItemModel.MapDomainObjectListToModel(x.InvoiceItems).ToArray(),
                Attributes = CallAttributeValueModel.Map(x.Attributes.Values),

                Owner = x.OwnerUser != null ? new PartialUserModel() { FullName = x.OwnerUser.FullName, Id = x.OwnerUser.Id } : null,
                Priority = CallPriority.Normal,

                // return notes as empty string instead of null - fixes android issue. 
                Notes = x.Notes ?? "",

                InvoiceNumber = x.InvoiceNumber,
                InvoiceTotal = x.InvoiceTotal,
                InvoiceSubtotal = x.InvoiceSubtotal,
                InvoiceTax = x.InvoiceTax,
                InvoiceTaxExempt = x.Invoice.IsTaxExempt,

                Status = new CallStatusUpdateModel() { Id = x.Status.Id },

                CreateDate = x.CreateDate,
                DispatchTime = x.DispatchTime,
                EnrouteTime = x.EnrouteTime,
                ArrivalETA = x.ArrivalETA,
                ArrivalTime = x.ArrivalTime,
                TowTime = x.TowTime,
                DestinationArrivalTime = x.DestinationArrivalTime,
                CompletionTime = x.CompletionTime,

                CancellationReason = x.CancellationReason,
                BalanceDue = x.BalanceDue,
                Payments = payments == null ? (InvoicePayment.GetByDispatchEntryId(x.Id, false)).Select(o => PaymentModel.Map(o)) :
                    payments.Where(o => o.InvoiceId == x.Invoice.Id || x.Invoice.Id < 1).Select(o => PaymentModel.Map(o)),
                Impound = x.Impound,
                InvoiceStatusId = x.InvoiceStatusId > 0 ? (int?)x.InvoiceStatusId : null,
                BillToAccountId = x.Invoice.AccountId,

                PurchaseOrderNumber = x.PurchaseOrderNumber,
                ReferenceUrl = x.Attributes.FirstOrDefault(o => o.Key == AttributeValue.BUILTIN_REFERENCE_URL).Value?.Value
            };

            if (!string.IsNullOrWhiteSpace(r.ReferenceUrl))
            {
                r.ReferenceUrlName = "View Instructions";

                if (x.Account?.MasterAccountId == MasterAccountTypes.Allstate)
                    r.ReferenceUrlName = "Vehicle Service Tips";
            }

            if (r.Id > ********)
            {
                if (r.Status?.Id == Dispatch.Status.Completed.Id)
                {
                    if (r.Attributes.Any(rx =>
                         rx.AttributeId == AttributeValue.BUILTIN_DISPATCH_COMPLETION_ACKNOWLEDGEMENT_JSON &&
                         rx.Value == "{}"))
                        r.Status.Id = Dispatch.Status.CompletedAcknowledgePending.Id;
                }
                else if (r.Status?.Id == Dispatch.Status.Cancelled.Id)
                {
                    if (r.Attributes.Any(rx =>
                         rx.AttributeId == AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON &&
                         rx.Value == "{}"))
                        r.Status.Id = Dispatch.Status.CancelledAcknowledgePending.Id;
                }
            }

            if (r.Payments != null)
            {
                r.Payments = r.Payments.Where(o => !o.IsVoid);
                r.PaymentsApplied = r.Payments.Sum(o => o.Amount);
            }

            if (r.BillToAccountId.GetValueOrDefault() > 0)
            {
                r.BillToAccountName = A.Account.GetById(r.BillToAccountId.Value)?.Company;
            }

            r.BalanceByClass = ClassBalanceModel.Map(r.InvoiceItems, r.Payments, r.InvoiceTax);

            #region Impound - REFACTOR NEEDED. THIS SHOULD NOT BE CALLED HERE
            // TODO: This needs to be refactored/rewritten.  if a company does a lot of impounds, this will slow them down.
            if (x.Impound == true)
            {
                var imp = impound ?? Impounds.Impound.GetByDispatchEntry(x);

                if (imp != null)
                {
                    if (imp.Lot != null)
                    {
                        r.ImpoundLotId = imp.Lot.Id;
                        r.TowDestination = imp.Lot.GetComposedAddress(imp.Company);
                    }

                    r.ImpoundDetails = new ImpoundDetailsModel();
                    r.ImpoundDetails.DaysHeld = imp.DaysHeldBillable;
                    r.ImpoundDetails.PoliceHold = imp.Hold;
                    r.ImpoundDetails.Id = imp.Id;
                    r.ImpoundDetails.ImpoundDate = imp.ImpoundDate;
                    r.ImpoundDetails.ReleaseDate = imp.ReleaseDate;
                    r.ImpoundDetails.ReleaseReason = Impounds.ReleaseReason.GetById(imp.ReleaseReason.GetValueOrDefault())?.Description ?? string.Empty;
                    r.ImpoundDetails.MotorVehicleReportDate = imp.MotorVehicleReportDate;
                    r.ImpoundDetails.Auction = imp.Auction;

                    // impound reason
                    if (!string.IsNullOrWhiteSpace(imp.Reason))
                    {
                        var cavm = new Collection<AttributeValue>();
                        cavm.Add(new AttributeValue()
                        {
                            DispatchEntryAttributeId = AttributeValue.BUILTIN_IMPOUND_REASON_ADHOC,
                            Value = imp.Reason
                        });

                        r.Attributes = r.Attributes?.Union(CallAttributeValueModel.Map(cavm.ToArray())).ToArray();
                    }

                    r.ImpoundDetails.Title = VehicleTitleModel.Map(vehicleTitles != null ? 
                        vehicleTitles?.FirstOrDefault(f => f.ImpoundId == imp.Id) : 
                        VehicleTitle.GetByImpoundId(imp.Id));

                    var ad = auctionDetails?.FirstOrDefault(f => f.DispatchEntryId == x.Id);
                    
                    // only fetch this call's auction detail from the db if the method caller did not provide any
                    if(ad == null && auctionDetails == null)
                        ad = EntryAuctionDetail.GetByDispatchEntryId(x.Id);
                    
                    if(ad != null)
                    {
                        r.AuctionDetails = AuctionDetailsModel.Map(ad);
                    }
                }
            }
            #endregion
            
            #region Assets
            var assets = new Collection<CallAssetModel>();

            if (x.Assets.Count == 0)
            {
                var fakeAsset = new CallAssetModel()
                {
                    Vin = x.VIN,
                    BodyType = BodyTypeModel.MapDomainToModel(x.BodyType),
                    Color = ColorModel.MapDomainToModel(x.Color),
                    Drivable = x.Drivable,
                    Truck = (x.Truck != null ? new PartialTruckModel() { Id = x.Truck.Id, Name = x.Truck.Name } : null),
                    Driver = (x.Driver != null ? new PartialDriverModel() { Id = x.Driver.Id, Name = x.Driver.Name } : null),
                    Make = x.VehicleMake,
                    Model = x.VehicleModel,
                    Year = (x.Year == 0 ? null : (int?)x.Year),
                    LicenseNumber = x.LicenseNumber,
                    LicenseState = x.LicenseState,
                    LicenseYear = x.LicenseYear,
                    Odometer = (x.Odometer == 0 ? null : (int?)x.Odometer),
                    Keys = null // TOOD: add Keys to Dispatch.Entry...
                };

                DriverTruckPairModel ead = new DriverTruckPairModel();

                if (x.TruckId > 0 && x.Truck != null)
                    ead.Truck = new PartialTruckModel() { Id = x.Truck.Id, Name = x.Truck.Name };

                if (x.DriverId > 0 && x.Driver != null)
                    ead.Driver = new PartialDriverModel() { Id = x.Driver.Id, Name = x.Driver.Name };

                if (ead.Driver != null || ead.Truck != null)
                    fakeAsset.Drivers = new DriverTruckPairModel[] { ead };
                else
                    fakeAsset.Drivers = Array.Empty<DriverTruckPairModel>();


                assets.Add(fakeAsset);
            }
            else
            {
                // clear out any "changes" that occured when just reading from db
                foreach(var asset in x.Assets)
                {
                    asset.ChangedFields = null;
                }
                var callAssets = x.Assets;

                foreach (var a in callAssets)
                {
                    var asset = new CallAssetModel()
                    {
                        Id = a.Id,
                        Vin = a.Vin,
                        BodyType = BodyTypeModel.MapDomainToModel(a.BodyTypeId),
                        Color = ColorModel.MapDomainToModel(a.ColorId),
                        Drivable = (a.Drivable == Drivable.Drivable ? true : a.Drivable == Drivable.NotDrivable ? false : (bool?)false),
                        DrivableId = (int)a.Drivable,
                        Make = a.Make,
                        Model = a.Model,
                        Year = (a.Year == 0 ? null : (int?)a.Year),
                        LicenseNumber = a.LicenseNumber,
                        LicenseState = a.LicenseState,
                        LicenseYear = a.LicenseYear.ToString(),
                        Odometer = (a.Odometer == 0 ? null : (int?)a.Odometer),
                        Keys = a.Keys,
                        KeysLocation = a.KeysLocation,
                        DriveType = a.DriveType,
                        Notes = a.Notes,
                        UnitNumber = a.UnitNumber,
                        AirbagStatus = (int)a.AirbagStatus
                    };

                    // for old clients that still use the asset.driver/asset.truck instead of the new/current drivers element. 
                    if (a.Drivers.Any())
                    {
                        if (a.Drivers[0].DriverId != null)
                        {
                            var driver = Extric.Towbook.Driver.GetById(a.Drivers[0].DriverId.Value);

                            if (driver != null)
                                asset.Driver = new PartialDriverModel() { Id = driver.Id, Name = driver.Name };
                        }
                        if (a.Drivers[0].TruckId != null)
                        {
                            var truck = Extric.Towbook.Truck.GetById(a.Drivers[0].TruckId.Value);
                            if (truck != null)
                                asset.Truck = new PartialTruckModel() { Id = truck.Id, Name = truck.Name };
                        }
                    }

                    var drivers = new Collection<DriverTruckPairModel>();
                    foreach (var driver in a.Drivers)
                    {
                        var dtp = new DriverTruckPairModel() { Id = driver.Id };

                        if (driver.DriverId != null)
                        {
                            var drv = Towbook.Driver.GetById(driver.DriverId.Value);
                            if (drv != null)
                                dtp.Driver = new PartialDriverModel()
                                {
                                    Id = drv.Id,
                                    Name = drv.Name,
                                    ResponseStatusId = driver.ResponseStatusId,
                                    ResponseTime = driver.ResponseTime,
                                    ResponseUserId = driver.ResponseUserId,
                                    CurrentWaypointId = driver.CurrentWaypointId
                                };
                        }

                        if (driver.TruckId != null)
                        {
                            var trk = Towbook.Truck.GetById(driver.TruckId.Value);
                            if (trk != null)
                                dtp.Truck = new PartialTruckModel() { Id = trk.Id, Name = trk.Name };
                        }

                        drivers.Add(dtp);
                    }

                    asset.Drivers = drivers.ToArray();

                    assets.Add(asset);
                }
            }

            r.Assets = assets.ToArray();

            #endregion

            #region Waypoints

            var waypoints = new Collection<CallWaypointModel>();

            if (!x.Waypoints.Any())
            {
                waypoints.Add(new CallWaypointModel()
                {
                    Id = -1,
                    Title = "Pickup",
                    Address = r.TowSource,
                    Position = 1,
                });

                waypoints.Add(new CallWaypointModel()
                {
                    Id = -2,
                    Title = "Destination",
                    Address = r.TowDestination,
                    Position = 2
                });
            }
            else
            {
                foreach (var a in x.Waypoints)
                {
                    var waypoint = CallWaypointModel.Map(a);

                    if (a.Title == "Pickup" || a.Title == "Service Location")
                        r.TowSource = a.Address;
                    else if (a.Title == "Destination" && !x.Impound) // dont replace tow dest with waypoint address if its an impound.
                        r.TowDestination = a.Address;

                    if (waypoint.Latitude == 0 || waypoint.Longitude == 0)
                    {
                        waypoint.Latitude = waypoint.Longitude = null;
                    }

                    waypoints.Add(waypoint);
                }
            }

            r.Waypoints = waypoints.ToArray();

            #endregion

            r.Tags = Array.Empty<int>(); // x.Tags.ToArray();
            r.Priority = (CallPriority)x.Priority;

            // groups/filtering
            r.Groups = Array.Empty<int>(); // DetermineGroupsForCall(r);

            if (prefilledInsights != null)
            {
                r.Insights = prefilledInsights;
            }
            else
            {
                r = AddInsightsToModel(r, insights);
            }

            // closed call insight
            if (x.Company.HasFeature(Features.AdvancedBilling_ClosedAccountingPeriod) && r.IsWithinClosedAccountingPeriod())
            {
                AddInsight(r, "isClosed", true);
            }

            return r;
        }

        public static async Task<CallModel> MapAsync(Entry x,
            IEnumerable<CallInsightHelper.CallInsightModel> insights,
            IEnumerable<InvoicePayment> payments,
            IDictionary<string, object> prefilledInsights = null,
            Impounds.Impound impound = null,
            IEnumerable<VehicleTitle> vehicleTitles = null,
            IEnumerable<EntryAuctionDetail> auctionDetails = null)
        {
            if (x == null)
                return null;

            var entryOwner = await x.GetOwnerUserAsync();
            var entryAccount = await x.GetAccountAsync();
            var entryAttributes = await x.GetAttributesAsync();
            var entryInvoice = await x.GetInvoiceAsync();
            var entryInvoiceTax = await x.GetInvoiceTaxAsync();
            var entryInvoiceSubTotal = await x.GetInvoiceSubtotalAsync();

            var r = new CallModel()
            {
                Id = x.Id,
                CallNumber = x.CallNumber,
                CompanyId = x.CompanyId,
                Type = x.Type,
                Reason = PartialReasonModel.Map(await x.GetReasonAsync()),
                TowSource = x.TowSource,
                TowDestination = x.TowDestination,

                Statements = x.Statements ?? Array.Empty<int>(),
                Version = x.Version,
                LastModifiedTimestamp = x.LastModifiedTimestamp,

                Contacts = CallContactModel.MapDomainObjectListToModel(await x.GetContactsAsync()),
                Account = PartialAccountModel.MapDomainToModel(entryAccount),
                Subcontractor = await SubcontractorModel.MapAsync(x),
                InvoiceItems = CallInvoiceItemModel.MapDomainObjectListToModel(await entryInvoice.GetInvoiceItemsAsync()).ToArray(),
                Attributes = CallAttributeValueModel.Map(entryAttributes.Values),

                Owner = entryOwner != null ? new PartialUserModel() { FullName = entryOwner.FullName, Id = entryOwner.Id } : null,
                Priority = CallPriority.Normal,

                // return notes as empty string instead of null - fixes android issue. 
                Notes = x.Notes ?? "",

                InvoiceNumber = x.InvoiceNumber,
                InvoiceTotal = await x.GetInvoiceTotalAsync(),
                InvoiceSubtotal = entryInvoiceSubTotal,
                InvoiceTax = entryInvoiceTax,
                InvoiceTaxExempt = entryInvoice.IsTaxExempt,

                Status = new CallStatusUpdateModel() { Id = x.Status.Id },

                CreateDate = x.CreateDate,
                DispatchTime = x.DispatchTime,
                EnrouteTime = x.EnrouteTime,
                ArrivalETA = x.ArrivalETA,
                ArrivalTime = x.ArrivalTime,
                TowTime = x.TowTime,
                DestinationArrivalTime = x.DestinationArrivalTime,
                CompletionTime = x.CompletionTime,

                CancellationReason = x.CancellationReason,
                BalanceDue = await x.GetBalanceDueAsync(),
                Payments = payments == null ? ((await InvoicePayment.GetByDispatchEntryIdAsync(x.Id, false))).Select(o => PaymentModel.Map(o)) :
                    payments.Where(o => o.InvoiceId == entryInvoice.Id || entryInvoice.Id < 1).Select(o => PaymentModel.Map(o)),
                Impound = x.Impound,
                InvoiceStatusId = x.InvoiceStatusId > 0 ? (int?)x.InvoiceStatusId : null,
                BillToAccountId = entryInvoice.AccountId,

                PurchaseOrderNumber = x.PurchaseOrderNumber,
                ReferenceUrl = entryAttributes.FirstOrDefault(o => o.Key == AttributeValue.BUILTIN_REFERENCE_URL).Value?.Value
            };

            if (!string.IsNullOrWhiteSpace(r.ReferenceUrl))
            {
                r.ReferenceUrlName = "View Instructions";

                if (entryAccount?.MasterAccountId == MasterAccountTypes.Allstate)
                    r.ReferenceUrlName = "Vehicle Service Tips";
            }

            if (r.Id > ********)
            {
                if (r.Status?.Id == Dispatch.Status.Completed.Id)
                {
                    if (r.Attributes.Any(rx =>
                         rx.AttributeId == AttributeValue.BUILTIN_DISPATCH_COMPLETION_ACKNOWLEDGEMENT_JSON &&
                         rx.Value == "{}"))
                        r.Status.Id = Dispatch.Status.CompletedAcknowledgePending.Id;
                }
                else if (r.Status?.Id == Dispatch.Status.Cancelled.Id)
                {
                    if (r.Attributes.Any(rx =>
                         rx.AttributeId == AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON &&
                         rx.Value == "{}"))
                        r.Status.Id = Dispatch.Status.CancelledAcknowledgePending.Id;
                }
            }

            if (r.Payments != null)
            {
                r.Payments = r.Payments.Where(o => !o.IsVoid);
                r.PaymentsApplied = r.Payments.Sum(o => o.Amount);
            }

            if (r.BillToAccountId.GetValueOrDefault() > 0)
            {
                r.BillToAccountName = (await A.Account.GetByIdAsync(r.BillToAccountId.Value))?.Company;
            }

            r.BalanceByClass = ClassBalanceModel.Map(r.InvoiceItems, r.Payments, r.InvoiceTax);

            #region Impound - REFACTOR NEEDED. THIS SHOULD NOT BE CALLED HERE
            // TODO: This needs to be refactored/rewritten.  if a company does a lot of impounds, this will slow them down.
            if (x.Impound == true)
            {
                var imp = impound ?? await Impounds.Impound.GetByDispatchEntryAsync(x);

                if (imp != null)
                {
                    var impLot = await imp.GetLotAsync();
                    if (impLot != null)
                    {
                        r.ImpoundLotId = impLot.Id;
                        r.TowDestination = await impLot.GetComposedAddressAsync(await imp.GetCompanyAsync());
                    }

                    r.ImpoundDetails = new ImpoundDetailsModel();
                    r.ImpoundDetails.DaysHeld = imp.DaysHeldBillable;
                    r.ImpoundDetails.PoliceHold = imp.Hold;
                    r.ImpoundDetails.Id = imp.Id;
                    r.ImpoundDetails.ImpoundDate = imp.ImpoundDate;
                    r.ImpoundDetails.ReleaseDate = imp.ReleaseDate;
                    r.ImpoundDetails.ReleaseReason = (await Impounds.ReleaseReason.GetByIdAsync(imp.ReleaseReason.GetValueOrDefault()))?.Description ?? string.Empty;
                    r.ImpoundDetails.MotorVehicleReportDate = imp.MotorVehicleReportDate;
                    r.ImpoundDetails.Auction = imp.Auction;

                    // impound reason
                    if (!string.IsNullOrWhiteSpace(imp.Reason))
                    {
                        var cavm = new Collection<AttributeValue>();
                        cavm.Add(new AttributeValue()
                        {
                            DispatchEntryAttributeId = AttributeValue.BUILTIN_IMPOUND_REASON_ADHOC,
                            Value = imp.Reason
                        });

                        r.Attributes = r.Attributes?.Union(CallAttributeValueModel.Map(cavm.ToArray())).ToArray();
                    }

                    r.ImpoundDetails.Title = VehicleTitleModel.Map(vehicleTitles != null ?
                        vehicleTitles?.FirstOrDefault(f => f.ImpoundId == imp.Id) :
                        await VehicleTitle.GetByImpoundIdAsync(imp.Id));

                    var ad = auctionDetails?.FirstOrDefault(f => f.DispatchEntryId == x.Id);

                    // only fetch this call's auction detail from the db if the method caller did not provide any
                    if (ad == null && auctionDetails == null)
                        ad = await EntryAuctionDetail.GetByDispatchEntryIdAsync(x.Id);

                    if (ad != null)
                    {
                        r.AuctionDetails = AuctionDetailsModel.Map(ad);
                    }
                }
            }
            #endregion

            #region Assets
            var assets = new Collection<CallAssetModel>();

            //TODO Async, requires QueryMultipleAsync
            if (x.Assets.Count == 0)
            {
                var entryTruck = await x.GetTruckAsync();
                var entryDriver = await x.GetDriverAsync();

                var fakeAsset = new CallAssetModel()
                {
                    Vin = x.VIN,
                    BodyType = BodyTypeModel.MapDomainToModel(await x.GetBodyTypeAsync()),
                    Color = ColorModel.MapDomainToModel(await x.GetColorAsync()),
                    Drivable = x.Drivable,
                    Truck = (entryTruck != null ? new PartialTruckModel() { Id = entryTruck.Id, Name = entryTruck.Name } : null),
                    Driver = (entryDriver != null ? new PartialDriverModel() { Id = entryDriver.Id, Name = entryDriver.Name } : null),
                    Make = x.VehicleMake,
                    Model = x.VehicleModel,
                    Year = (x.Year == 0 ? null : (int?)x.Year),
                    LicenseNumber = x.LicenseNumber,
                    LicenseState = x.LicenseState,
                    LicenseYear = x.LicenseYear,
                    Odometer = (x.Odometer == 0 ? null : (int?)x.Odometer),
                    Keys = null // TOOD: add Keys to Dispatch.Entry...
                };

                DriverTruckPairModel ead = new DriverTruckPairModel();

                if (x.TruckId > 0 && entryTruck != null)
                    ead.Truck = new PartialTruckModel() { Id = entryTruck.Id, Name = entryTruck.Name };

                if (x.DriverId > 0 && entryDriver != null)
                    ead.Driver = new PartialDriverModel() { Id = entryDriver.Id, Name = entryDriver.Name };

                if (ead.Driver != null || ead.Truck != null)
                    fakeAsset.Drivers = new DriverTruckPairModel[] { ead };
                else
                    fakeAsset.Drivers = Array.Empty<DriverTruckPairModel>();


                assets.Add(fakeAsset);
            }
            else
            {
                // clear out any "changes" that occured when just reading from db
                foreach (var asset in x.Assets)
                {
                    asset.ChangedFields = null;
                }
                var callAssets = x.Assets;

                foreach (var a in callAssets)
                {
                    var asset = new CallAssetModel()
                    {
                        Id = a.Id,
                        Vin = a.Vin,
                        BodyType = await BodyTypeModel.MapDomainToModelAsync(a.BodyTypeId),
                        Color = await ColorModel.MapDomainToModelAsync(a.ColorId),
                        Drivable = (a.Drivable == Drivable.Drivable ? true : a.Drivable == Drivable.NotDrivable ? false : (bool?)false),
                        DrivableId = (int)a.Drivable,
                        Make = a.Make,
                        Model = a.Model,
                        Year = (a.Year == 0 ? null : (int?)a.Year),
                        LicenseNumber = a.LicenseNumber,
                        LicenseState = a.LicenseState,
                        LicenseYear = a.LicenseYear.ToString(),
                        Odometer = (a.Odometer == 0 ? null : (int?)a.Odometer),
                        Keys = a.Keys,
                        KeysLocation = a.KeysLocation,
                        DriveType = a.DriveType,
                        Notes = a.Notes,
                        UnitNumber = a.UnitNumber,
                        AirbagStatus = (int)a.AirbagStatus
                    };

                    // for old clients that still use the asset.driver/asset.truck instead of the new/current drivers element. 
                    if (a.Drivers.Any())
                    {
                        if (a.Drivers[0].DriverId != null)
                        {
                            var driver = await Extric.Towbook.Driver.GetByIdAsync(a.Drivers[0].DriverId.Value);

                            if (driver != null)
                                asset.Driver = new PartialDriverModel() { Id = driver.Id, Name = driver.Name };
                        }
                        if (a.Drivers[0].TruckId != null)
                        {
                            var truck = await Extric.Towbook.Truck.GetByIdAsync(a.Drivers[0].TruckId.Value);
                            if (truck != null)
                                asset.Truck = new PartialTruckModel() { Id = truck.Id, Name = truck.Name };
                        }
                    }

                    var drivers = new Collection<DriverTruckPairModel>();
                    foreach (var driver in a.Drivers)
                    {
                        var dtp = new DriverTruckPairModel() { Id = driver.Id };

                        if (driver.DriverId != null)
                        {
                            var drv = await Towbook.Driver.GetByIdAsync(driver.DriverId.Value);
                            if (drv != null)
                                dtp.Driver = new PartialDriverModel()
                                {
                                    Id = drv.Id,
                                    Name = drv.Name,
                                    ResponseStatusId = driver.ResponseStatusId,
                                    ResponseTime = driver.ResponseTime,
                                    ResponseUserId = driver.ResponseUserId,
                                    CurrentWaypointId = driver.CurrentWaypointId
                                };
                        }

                        if (driver.TruckId != null)
                        {
                            var trk = await Towbook.Truck.GetByIdAsync(driver.TruckId.Value);
                            if (trk != null)
                                dtp.Truck = new PartialTruckModel() { Id = trk.Id, Name = trk.Name };
                        }

                        drivers.Add(dtp);
                    }

                    asset.Drivers = drivers.ToArray();

                    assets.Add(asset);
                }
            }

            r.Assets = assets.ToArray();

            #endregion

            #region Waypoints

            var waypoints = new Collection<CallWaypointModel>();

            if (!x.Waypoints.Any())
            {
                waypoints.Add(new CallWaypointModel()
                {
                    Id = -1,
                    Title = "Pickup",
                    Address = r.TowSource,
                    Position = 1,
                });

                waypoints.Add(new CallWaypointModel()
                {
                    Id = -2,
                    Title = "Destination",
                    Address = r.TowDestination,
                    Position = 2
                });
            }
            else
            {
                foreach (var a in x.Waypoints)
                {
                    var waypoint = CallWaypointModel.Map(a);

                    if (a.Title == "Pickup" || a.Title == "Service Location")
                        r.TowSource = a.Address;
                    else if (a.Title == "Destination" && !x.Impound) // dont replace tow dest with waypoint address if its an impound.
                        r.TowDestination = a.Address;

                    if (waypoint.Latitude == 0 || waypoint.Longitude == 0)
                    {
                        waypoint.Latitude = waypoint.Longitude = null;
                    }

                    waypoints.Add(waypoint);
                }
            }

            r.Waypoints = waypoints.ToArray();

            #endregion

            r.Tags = Array.Empty<int>(); // x.Tags.ToArray();
            r.Priority = (CallPriority)x.Priority;

            // groups/filtering
            r.Groups = Array.Empty<int>(); // DetermineGroupsForCall(r);

            if (prefilledInsights != null)
            {
                r.Insights = prefilledInsights;
            }
            else
            {
                r = await AddInsightsToModelAsync(r, insights);
            }

            // closed call insight
            if (await x.Company.HasFeatureAsync(Features.AdvancedBilling_ClosedAccountingPeriod) && await r.IsWithinClosedAccountingPeriodAsync())
            {
                AddInsight(r, "isClosed", true);
            }

            return r;
        }

        public class CallInsightHelper
        {
            public class CallInsightModel
            {
                public int DispatchEntryId { get; set; }
                public string Name { get; set; }
                public string Value { get; set; }
                public int ValueAsInt
                {
                    get
                    {
                        int n = 0;
                        int.TryParse(Value, out n);
                        return n;
                    }
                }
            }

            public static IEnumerable<CallInsightModel> GetByDispatchEntries(int[] ids)
            {
                Collection<CallInsightModel> list = new Collection<CallInsightModel>();

                foreach (var batch in ids.OrderBy(o => o).Batch(50))
                {
                    var results = SqlMapper.QuerySP<CallInsightModel>("DispatchEntryInsightsGetByArray",
                        new { DispatchEntryIds = string.Join(",", batch) },
                        commandTimeout: 10000);

                    list = list.Union(results).ToCollection();
                }

                return list;
            }
            
            public static async Task<IEnumerable<CallInsightModel>> GetByDispatchEntriesAsync(int[] ids)
            {
                Collection<CallInsightModel> list = new Collection<CallInsightModel>();

                foreach (var batch in ids.OrderBy(o => o).Batch(50))
                {
                    // TODO: Could be optimized to not make n calls.
                    var results = await SqlMapper.QuerySpAsync<CallInsightModel>("DispatchEntryInsightsGetByArray",
                        new { DispatchEntryIds = string.Join(",", batch) },
                        commandTimeout: 10000);

                    list = list.Union(results).ToCollection();
                }

                return list;
            }
        }

        private static CallModel AddInsightsToModel(CallModel m, IEnumerable<CallInsightHelper.CallInsightModel> insights)
        {
            if (m.Insights != null && m.Insights.Count > 0)
                return m;

            if (insights == null)
                insights = CallInsightHelper.GetByDispatchEntries(new int[] { m.Id });

            var outputRow = new Dictionary<string, object>();

            foreach (var row in insights.Where(z => z.DispatchEntryId == m.Id))
            {
                outputRow[row.Name] = row.Value;
            }

            m.Insights = outputRow;

            return m;
        }

        private static async Task<CallModel> AddInsightsToModelAsync(CallModel m, IEnumerable<CallInsightHelper.CallInsightModel> insights)
        {
            if (m.Insights != null && m.Insights.Count > 0)
                return m;

            if (insights == null)
                insights = await CallInsightHelper.GetByDispatchEntriesAsync(new int[] { m.Id });

            var outputRow = new Dictionary<string, object>();

            foreach (var row in insights.Where(z => z.DispatchEntryId == m.Id))
            {
                outputRow[row.Name] = row.Value;
            }

            m.Insights = outputRow;

            return m;
        }

        public static void AddInsight(CallModel m, CallInsightHelper.CallInsightModel insight)
        {
            if (insight == null ||
                string.IsNullOrWhiteSpace(insight.Name) ||
                string.IsNullOrWhiteSpace(insight.Value))
                return;

            if (m.Insights == null)
                m.Insights = new Dictionary<string, object>();

            // add new insight
            m.Insights[insight.Name] = insight.Value;
        }

        public static void AddInsight(CallModel m, string name, bool value)
        {
            if (m == null || string.IsNullOrEmpty(name))
                return;

            if (m.Insights == null)
                m.Insights = new Dictionary<string, object>();

            // add new insight
            m.Insights[name] = value;
        }

        /// <summary>
        /// Get the number of properties set on this call by serializing and deserializing
        /// the call as json.
        /// </summary>
        public int GetPropertyCount()
        {
            string callJson = Newtonsoft.Json.JsonConvert.SerializeObject(this,
                Newtonsoft.Json.Formatting.Indented,
                new Newtonsoft.Json.JsonSerializerSettings()
                {
                    NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore,
                    DefaultValueHandling = Newtonsoft.Json.DefaultValueHandling.Ignore
                });

            Newtonsoft.Json.Linq.JObject parsedCallJson = Newtonsoft.Json.Linq.JObject.Parse(callJson);
            return parsedCallJson.Properties().Count();
        }
    }

    public class CallChannelModel
    {
        public string Type { get; set; }
        public string Name { get; set; }
    }
}
